﻿<?php if (isset($component)) { $__componentOriginal895f6ef515592ffd4805667c75b9d7a7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal895f6ef515592ffd4805667c75b9d7a7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.dashboard-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dashboard-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="p-6 space-y-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h2 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Jewel Pro Dashboard</h2>
            <p class="text-gray-600">Your professional jewelry management system is ready!</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                <div class="stat-card rounded-xl p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Today's Sales</p>
                            <p class="text-3xl font-bold text-gray-900 mt-2">₹<?php echo e(number_format($stats['today_sales'] ?? 0, 0)); ?></p>
                        </div>
                        <div class="w-16 h-16 success-gradient rounded-xl flex items-center justify-center">
                            <i class="fas fa-rupee-sign text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card rounded-xl p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Total Customers</p>
                            <p class="text-3xl font-bold text-gray-900 mt-2"><?php echo e(number_format($stats['total_customers'] ?? 0)); ?></p>
                        </div>
                        <div class="w-16 h-16 gradient-bg rounded-xl flex items-center justify-center">
                            <i class="fas fa-users text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card rounded-xl p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Products in Stock</p>
                            <p class="text-3xl font-bold text-gray-900 mt-2"><?php echo e(number_format($stats['total_products'] ?? 0)); ?></p>
                        </div>
                        <div class="w-16 h-16 gold-gradient rounded-xl flex items-center justify-center">
                            <i class="fas fa-gem text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card rounded-xl p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Pending Orders</p>
                            <p class="text-3xl font-bold text-gray-900 mt-2"><?php echo e(number_format($stats['pending_estimates'] ?? 0)); ?></p>
                        </div>
                        <div class="w-16 h-16 danger-gradient rounded-xl flex items-center justify-center">
                            <i class="fas fa-clock text-white text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal895f6ef515592ffd4805667c75b9d7a7)): ?>
<?php $attributes = $__attributesOriginal895f6ef515592ffd4805667c75b9d7a7; ?>
<?php unset($__attributesOriginal895f6ef515592ffd4805667c75b9d7a7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal895f6ef515592ffd4805667c75b9d7a7)): ?>
<?php $component = $__componentOriginal895f6ef515592ffd4805667c75b9d7a7; ?>
<?php unset($__componentOriginal895f6ef515592ffd4805667c75b9d7a7); ?>
<?php endif; ?>
<?php /**PATH C:\proj\Jewel Pro\jewel-pro\resources\views/dashboard.blade.php ENDPATH**/ ?>